import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type',
}

interface ContactData {
  id: string
  name: string
  email: string
  message: string
  source?: string
  created_at: string
}

interface MailgunResponse {
  id: string
  message: string
}

serve(async req => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🚀 Contact email Edge Function triggered')

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const mailgunApiKey = Deno.env.get('MAILGUN_API_KEY')!
    const mailgunDomain = Deno.env.get('MAILGUN_DOMAIN') || 'fokas.tech'
    const adminEmail = Deno.env.get('ADMIN_EMAIL') || '<EMAIL>'

    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey || !mailgunApiKey) {
      throw new Error('Missing required environment variables')
    }

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Parse request body
    const contactData: ContactData = await req.json()
    console.log('📧 Processing contact:', contactData.email)

    // Update contact status to 'sent' and increment delivery attempts
    const { error: updateError } = await supabase
      .from('contacts')
      .update({
        email_status: 'sent',
        delivery_attempts: 1,
        last_delivery_attempt: new Date().toISOString(),
      })
      .eq('id', contactData.id)

    if (updateError) {
      console.error('❌ Failed to update contact status:', updateError)
    }

    // Send admin notification email
    const adminEmailResult = await sendAdminNotification(
      contactData,
      mailgunApiKey,
      mailgunDomain,
      adminEmail
    )

    // Send auto-reply to user
    const autoReplyResult = await sendAutoReply(
      contactData,
      mailgunApiKey,
      mailgunDomain
    )

    // Update contact with Mailgun message IDs
    if (adminEmailResult.success || autoReplyResult.success) {
      const mailgunMessageId =
        adminEmailResult.messageId || autoReplyResult.messageId
      await supabase
        .from('contacts')
        .update({
          mailgun_message_id: mailgunMessageId,
        })
        .eq('id', contactData.id)
    }

    console.log('✅ Contact email processing completed')

    return new Response(
      JSON.stringify({
        success: true,
        adminEmail: adminEmailResult,
        autoReply: autoReplyResult,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('❌ Contact email function error:', error)
    return new Response(
      JSON.stringify({
        error: error.message,
        success: false,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

async function sendAdminNotification(
  contactData: ContactData,
  apiKey: string,
  domain: string,
  adminEmail: string
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    console.log('📨 Sending admin notification...')

    const formData = new FormData()
    formData.append('from', `Denis Portfolio <denis@${domain}>`)
    formData.append('to', adminEmail)
    formData.append('subject', `🚨 New Contact: ${contactData.name}`)
    formData.append('html', generateAdminEmailHTML(contactData))
    formData.append('text', generateAdminEmailText(contactData))

    // Add custom variables for webhook tracking
    formData.append('v:contact_id', contactData.id)
    formData.append('v:email_type', 'admin_notification')

    const response = await fetch(
      `https://api.mailgun.net/v3/${domain}/messages`,
      {
        method: 'POST',
        headers: {
          Authorization: `Basic ${btoa(`api:${apiKey}`)}`,
        },
        body: formData,
      }
    )

    const result: MailgunResponse = await response.json()

    if (response.ok) {
      console.log('✅ Admin notification sent:', result.id)
      return { success: true, messageId: result.id }
    } else {
      console.error('❌ Admin notification failed:', result)
      return { success: false, error: result.message }
    }
  } catch (error) {
    console.error('❌ Admin notification error:', error)
    return { success: false, error: error.message }
  }
}

async function sendAutoReply(
  contactData: ContactData,
  apiKey: string,
  domain: string
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    console.log('📨 Sending auto-reply...')

    const formData = new FormData()
    formData.append('from', `Denis Erastus <denis@${domain}>`)
    formData.append('to', contactData.email)
    formData.append('subject', 'Thank you for reaching out! 🚀')
    formData.append('html', generateAutoReplyHTML(contactData))
    formData.append('text', generateAutoReplyText(contactData))

    // Add custom variables for webhook tracking
    formData.append('v:contact_id', contactData.id)
    formData.append('v:email_type', 'auto_reply')

    const response = await fetch(
      `https://api.mailgun.net/v3/${domain}/messages`,
      {
        method: 'POST',
        headers: {
          Authorization: `Basic ${btoa(`api:${apiKey}`)}`,
        },
        body: formData,
      }
    )

    const result: MailgunResponse = await response.json()

    if (response.ok) {
      console.log('✅ Auto-reply sent:', result.id)
      return { success: true, messageId: result.id }
    } else {
      console.error('❌ Auto-reply failed:', result)
      return { success: false, error: result.message }
    }
  } catch (error) {
    console.error('❌ Auto-reply error:', error)
    return { success: false, error: error.message }
  }
}

function generateAdminEmailHTML(contact: ContactData): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Contact Submission</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #17b8dd; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #17b8dd; }
        .value { margin-top: 5px; padding: 10px; background: white; border-radius: 4px; }
        .footer { margin-top: 20px; padding: 15px; background: #e9e9e9; border-radius: 4px; font-size: 12px; color: #666; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>🚨 New Contact Submission</h2>
          <p>Status: Email pending delivery confirmation</p>
        </div>
        <div class="content">
          <div class="field">
            <div class="label">Name:</div>
            <div class="value">${contact.name}</div>
          </div>
          <div class="field">
            <div class="label">Email:</div>
            <div class="value">${contact.email}</div>
          </div>
          <div class="field">
            <div class="label">Message:</div>
            <div class="value">${contact.message}</div>
          </div>
          <div class="field">
            <div class="label">Source:</div>
            <div class="value">${contact.source}</div>
          </div>
          <div class="field">
            <div class="label">Submitted:</div>
            <div class="value">${new Date(contact.created_at).toLocaleString()}</div>
          </div>
        </div>
        <div class="footer">
          <p><strong>Next Steps:</strong></p>
          <ul>
            <li>Respond within 24 hours</li>
            <li>Check for delivery confirmation</li>
            <li>If email bounces, try alternative contact methods</li>
          </ul>
        </div>
      </div>
    </body>
    </html>
  `
}

function generateAdminEmailText(contact: ContactData): string {
  return `
🚨 NEW CONTACT SUBMISSION

Name: ${contact.name}
Email: ${contact.email}
Message: ${contact.message}
Source: ${contact.source}
Submitted: ${new Date(contact.created_at).toLocaleString()}

Status: Email pending delivery confirmation

Next Steps:
- Respond within 24 hours
- Check for delivery confirmation
- If email bounces, try alternative contact methods
  `
}

function generateAutoReplyHTML(contact: ContactData): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Thank you for reaching out!</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #17b8dd; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
        .highlight { color: #17b8dd; font-weight: bold; }
        .footer { margin-top: 20px; padding: 15px; background: #e9e9e9; border-radius: 4px; font-size: 12px; color: #666; text-align: center; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>🚀 Thank You for Reaching Out!</h2>
        </div>
        <div class="content">
          <p>Hi ${contact.name},</p>
          
          <p>Thank you for your message! I've received your inquiry and will respond within <span class="highlight">24 hours</span>.</p>
          
          <p>Your message:</p>
          <blockquote style="border-left: 4px solid #17b8dd; padding-left: 15px; margin: 15px 0; font-style: italic;">
            "${contact.message}"
          </blockquote>
          
          <p>In the meantime, feel free to:</p>
          <ul>
            <li>Check out my <a href="https://my-portfolio-git-develop-devdenis-projects.vercel.app/" style="color: #17b8dd;">portfolio</a></li>
            <li>Book a <a href="https://my-portfolio-git-develop-devdenis-projects.vercel.app/#hero" style="color: #17b8dd;">consultation call</a></li>
            <li>Connect with me on <a href="https://linkedin.com/in/denis-erastus" style="color: #17b8dd;">LinkedIn</a></li>
          </ul>
          
          <p>Looking forward to discussing how AI automation can transform your business!</p>
          
          <p>Best regards,<br>
          <strong>Denis Erastus</strong><br>
          AI & Business Automation Expert</p>
        </div>
        <div class="footer">
          <p>This is an automated response. Please don't reply to this email.</p>
          <p>If urgent, you can reach me <NAME_EMAIL></p>
        </div>
      </div>
    </body>
    </html>
  `
}

function generateAutoReplyText(contact: ContactData): string {
  return `
Hi ${contact.name},

Thank you for your message! I've received your inquiry and will respond within 24 hours.

Your message:
"${contact.message}"

In the meantime, feel free to:
- Check out my portfolio: https://my-portfolio-git-develop-devdenis-projects.vercel.app/
- Book a consultation call: https://my-portfolio-git-develop-devdenis-projects.vercel.app/#hero
- Connect with me on LinkedIn: https://linkedin.com/in/denis-erastus

Looking forward to discussing how AI automation can transform your business!

Best regards,
Denis Erastus
AI & Business Automation Expert

---
This is an automated response. Please don't reply to this email.
If urgent, you can reach me <NAME_EMAIL>
  `
}
