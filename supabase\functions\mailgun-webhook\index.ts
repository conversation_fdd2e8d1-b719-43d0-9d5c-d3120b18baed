import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { createHmac } from "https://deno.land/std@0.168.0/node/crypto.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface MailgunWebhookEvent {
  'event-data': {
    event: string
    timestamp: number
    id: string
    message: {
      headers: {
        'message-id': string
      }
    }
    recipient: string
    'delivery-status': {
      message?: string
      description?: string
      code?: number
    }
    'user-variables': {
      contact_id?: string
      email_type?: string
    }
    reason?: string
    severity?: string
  }
  signature: {
    timestamp: string
    token: string
    signature: string
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🎣 Mailgun webhook received')

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const mailgunWebhookSigningKey = Deno.env.get('MAILGUN_WEBHOOK_SIGNING_KEY')!
    const mailgunApiKey = Deno.env.get('MAILGUN_API_KEY')!
    const mailgunDomain = Deno.env.get('MAILGUN_DOMAIN') || 'fokas.tech'
    const adminEmail = Deno.env.get('ADMIN_EMAIL') || '<EMAIL>'

    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey || !mailgunWebhookSigningKey) {
      throw new Error('Missing required environment variables')
    }

    // Parse webhook data
    const webhookData: MailgunWebhookEvent = await req.json()
    const eventData = webhookData['event-data']
    
    console.log('📧 Webhook event:', eventData.event, 'for:', eventData.recipient)

    // Verify webhook signature (security)
    const isValid = verifyWebhookSignature(webhookData, mailgunWebhookSigningKey)
    if (!isValid) {
      console.error('❌ Invalid webhook signature')
      return new Response('Unauthorized', { status: 401 })
    }

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Process different event types
    switch (eventData.event) {
      case 'delivered':
        await handleDelivered(supabase, eventData)
        break
      
      case 'failed':
      case 'bounced':
        await handleBounced(supabase, eventData, mailgunApiKey, mailgunDomain, adminEmail)
        break
      
      case 'complained':
        await handleComplained(supabase, eventData)
        break
      
      case 'unsubscribed':
        await handleUnsubscribed(supabase, eventData)
        break
      
      default:
        console.log('ℹ️ Unhandled event type:', eventData.event)
    }

    return new Response(
      JSON.stringify({ success: true, event: eventData.event }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('❌ Webhook processing error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

function verifyWebhookSignature(webhookData: MailgunWebhookEvent, signingKey: string): boolean {
  try {
    const { timestamp, token, signature } = webhookData.signature
    
    // Create the signature string
    const signatureData = timestamp + token
    
    // Create HMAC
    const hmac = createHmac('sha256', signingKey)
    hmac.update(signatureData)
    const computedSignature = hmac.digest('hex')
    
    return computedSignature === signature
  } catch (error) {
    console.error('❌ Signature verification error:', error)
    return false
  }
}

async function handleDelivered(supabase: any, eventData: any) {
  console.log('✅ Email delivered successfully')
  
  const contactId = eventData['user-variables']?.contact_id
  if (!contactId) {
    console.log('ℹ️ No contact_id in user variables, skipping database update')
    return
  }

  const { error } = await supabase
    .from('contacts')
    .update({
      email_status: 'delivered',
      delivered_at: new Date(eventData.timestamp * 1000).toISOString()
    })
    .eq('id', contactId)

  if (error) {
    console.error('❌ Failed to update delivery status:', error)
  } else {
    console.log('✅ Contact delivery status updated')
  }
}

async function handleBounced(supabase: any, eventData: any, apiKey: string, domain: string, adminEmail: string) {
  console.log('❌ Email bounced:', eventData.reason)
  
  const contactId = eventData['user-variables']?.contact_id
  const bounceReason = eventData.reason || eventData['delivery-status']?.description || 'Unknown bounce reason'
  
  // Update contact status
  if (contactId) {
    const { error } = await supabase
      .from('contacts')
      .update({
        email_status: 'bounced',
        bounce_reason: bounceReason,
        bounced_at: new Date(eventData.timestamp * 1000).toISOString()
      })
      .eq('id', contactId)

    if (error) {
      console.error('❌ Failed to update bounce status:', error)
    } else {
      console.log('✅ Contact bounce status updated')
    }

    // Get contact details for bounce alert
    const { data: contact } = await supabase
      .from('contacts')
      .select('*')
      .eq('id', contactId)
      .single()

    if (contact) {
      // Send bounce alert to admin
      await sendBounceAlert(contact, bounceReason, apiKey, domain, adminEmail)
    }
  }
}

async function handleComplained(supabase: any, eventData: any) {
  console.log('⚠️ Email marked as spam')
  
  const contactId = eventData['user-variables']?.contact_id
  if (!contactId) return

  const { error } = await supabase
    .from('contacts')
    .update({
      email_status: 'complained'
    })
    .eq('id', contactId)

  if (error) {
    console.error('❌ Failed to update complaint status:', error)
  }
}

async function handleUnsubscribed(supabase: any, eventData: any) {
  console.log('🚫 User unsubscribed')
  
  const contactId = eventData['user-variables']?.contact_id
  if (!contactId) return

  const { error } = await supabase
    .from('contacts')
    .update({
      email_status: 'unsubscribed'
    })
    .eq('id', contactId)

  if (error) {
    console.error('❌ Failed to update unsubscribe status:', error)
  }
}

async function sendBounceAlert(contact: any, bounceReason: string, apiKey: string, domain: string, adminEmail: string) {
  try {
    console.log('🚨 Sending bounce alert to admin')

    const formData = new FormData()
    formData.append('from', `Denis Portfolio <denis@${domain}>`)
    formData.append('to', adminEmail)
    formData.append('subject', `🚨 BOUNCED: Contact needs manual follow-up - ${contact.name}`)
    formData.append('html', generateBounceAlertHTML(contact, bounceReason))
    formData.append('text', generateBounceAlertText(contact, bounceReason))

    const response = await fetch(`https://api.mailgun.net/v3/${domain}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`api:${apiKey}`)}`
      },
      body: formData
    })

    if (response.ok) {
      console.log('✅ Bounce alert sent to admin')
    } else {
      console.error('❌ Failed to send bounce alert')
    }

  } catch (error) {
    console.error('❌ Bounce alert error:', error)
  }
}

function generateBounceAlertHTML(contact: any, bounceReason: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Email Bounced - Manual Follow-up Required</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #e74c3c; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #e74c3c; }
        .value { margin-top: 5px; padding: 10px; background: white; border-radius: 4px; }
        .bounce-reason { background: #ffebee; border-left: 4px solid #e74c3c; padding: 15px; margin: 15px 0; }
        .actions { background: #e8f5e8; padding: 15px; border-radius: 4px; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>🚨 BOUNCED: Contact needs manual follow-up</h2>
        </div>
        <div class="content">
          <div class="field">
            <div class="label">Name:</div>
            <div class="value">${contact.name}</div>
          </div>
          <div class="field">
            <div class="label">❌ Email (BOUNCED):</div>
            <div class="value">${contact.email}</div>
          </div>
          <div class="field">
            <div class="label">Message:</div>
            <div class="value">${contact.message}</div>
          </div>
          <div class="field">
            <div class="label">Source:</div>
            <div class="value">${contact.source}</div>
          </div>
          
          <div class="bounce-reason">
            <strong>Bounce Reason:</strong> ${bounceReason}
          </div>
          
          <div class="actions">
            <h3>🎯 Suggested Next Steps:</h3>
            <ul>
              <li>Check for phone number in the message</li>
              <li>Look up "${contact.name}" on LinkedIn</li>
              <li>Try alternative email spellings</li>
              <li>Search for company contact information</li>
              <li>Check if it's a typo in the email address</li>
            </ul>
          </div>
        </div>
      </div>
    </body>
    </html>
  `
}

function generateBounceAlertText(contact: any, bounceReason: string): string {
  return `
🚨 BOUNCED: Contact needs manual follow-up

Name: ${contact.name}
❌ Email (BOUNCED): ${contact.email}
Message: ${contact.message}
Source: ${contact.source}

Bounce Reason: ${bounceReason}

🎯 Suggested Next Steps:
- Check for phone number in the message
- Look up "${contact.name}" on LinkedIn
- Try alternative email spellings
- Search for company contact information
- Check if it's a typo in the email address

This contact attempted to reach you but their email bounced. Manual follow-up is required.
  `
}
