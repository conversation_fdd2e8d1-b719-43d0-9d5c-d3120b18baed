# Email Notifications Setup Instructions

## 🎯 Quick Setup Guide

Follow these steps to complete the email notification system setup:

## 1. 📧 Get Mailgun Credentials

### API Key
1. Go to [Mailgun Dashboard](https://app.mailgun.com/app/dashboard)
2. Navigate to **Settings** → **API Keys**
3. Copy your **Private API Key** (starts with `key-`)

### Webhook Signing Key
1. In Mailgun Dashboard, go to **Settings** → **Webhooks**
2. Copy the **HTTP webhook signing key**

## 2. 🔧 Set Supabase Environment Variables

1. Go to [Supabase Dashboard](https://supabase.com/dashboard/project/hkbhttezrkjbyoougifv)
2. Navigate to **Edge Functions** → **Environment Variables**
3. Add these variables:

```
MAILGUN_API_KEY=key-your-actual-api-key-here
MAILGUN_WEBHOOK_SIGNING_KEY=your-webhook-signing-key-here
MAILGUN_DOMAIN=fokas.tech
ADMIN_EMAIL=<EMAIL>
```

## 3. 🚀 Deploy Edge Functions

Run the deployment script:

```bash
# Make sure you're in the project root
cd /path/to/your/project

# Run the deployment script
./scripts/deploy-edge-functions.sh
```

Or deploy manually:

```bash
# Login to Supabase (if not already logged in)
supabase login

# Deploy functions
supabase functions deploy send-contact-email --project-ref hkbhttezrkjbyoougifv
supabase functions deploy mailgun-webhook --project-ref hkbhttezrkjbyoougifv
```

## 4. 🎣 Configure Mailgun Webhooks

1. Go to [Mailgun Dashboard](https://app.mailgun.com/app/dashboard)
2. Navigate to **Sending** → **Webhooks**
3. Select your domain: `fokas.tech`
4. Add these webhook URLs:

### Webhook Configuration:
- **URL**: `https://hkbhttezrkjbyoougifv.supabase.co/functions/v1/mailgun-webhook`
- **Events to track**:
  - ✅ **delivered** - Email successfully delivered
  - ✅ **failed** - Email failed to deliver
  - ✅ **bounced** - Email bounced (hard bounce)
  - ✅ **complained** - Email marked as spam
  - ✅ **unsubscribed** - User unsubscribed

## 5. 🧪 Test the System

### Test Contact Form Submission:
1. Go to your website: [https://my-portfolio-git-develop-devdenis-projects.vercel.app/](https://my-portfolio-git-develop-devdenis-projects.vercel.app/)
2. Click the floating contact button
3. Choose "Contact Form"
4. Fill out and submit the form
5. Check your email (<EMAIL>) for the admin notification

### Expected Results:
- ✅ Form submits successfully
- ✅ Admin receives notification email within 30 seconds
- ✅ User receives auto-reply email within 30 seconds
- ✅ Database shows contact with `email_status = 'sent'`
- ✅ After delivery, status updates to `delivered`

## 6. 📊 Monitor the System

### Check Database Status:
```sql
-- View recent contacts and their email status
SELECT 
  name, 
  email, 
  email_status, 
  created_at,
  delivered_at,
  bounce_reason
FROM contacts 
ORDER BY created_at DESC 
LIMIT 10;
```

### Check Edge Function Logs:
1. Go to Supabase Dashboard → **Edge Functions**
2. Click on function name to view logs
3. Monitor for any errors or issues

### Check Mailgun Logs:
1. Go to Mailgun Dashboard → **Sending** → **Logs**
2. Monitor email delivery status
3. Check for bounces or failures

## 🔧 Troubleshooting

### Common Issues:

#### 1. Edge Functions Not Deploying
```bash
# Check if you're logged in
supabase projects list

# If not logged in
supabase login

# Try deploying again
supabase functions deploy send-contact-email --project-ref hkbhttezrkjbyoougifv
```

#### 2. Emails Not Sending
- ✅ Verify `MAILGUN_API_KEY` is correct
- ✅ Check Mailgun domain is verified
- ✅ Ensure `fokas.tech` domain is active in Mailgun

#### 3. Webhooks Not Working
- ✅ Verify `MAILGUN_WEBHOOK_SIGNING_KEY` is correct
- ✅ Check webhook URL is accessible
- ✅ Ensure webhook events are properly configured

#### 4. Database Trigger Not Firing
```sql
-- Check if trigger exists
SELECT * FROM pg_trigger WHERE tgname = 'on_contact_created';

-- Check trigger function
SELECT * FROM pg_proc WHERE proname = 'notify_new_contact';
```

### Debug Commands:

```sql
-- Check recent Edge Function calls
SELECT * FROM net.http_request_queue ORDER BY created_at DESC LIMIT 5;

-- Check contact email statuses
SELECT email_status, COUNT(*) FROM contacts GROUP BY email_status;

-- Check recent bounces
SELECT * FROM contacts WHERE email_status = 'bounced' ORDER BY bounced_at DESC;
```

## 📈 Success Metrics

After setup, you should see:
- **Delivery Rate**: 85-95%
- **Admin Notification**: < 30 seconds
- **Auto-Reply**: < 30 seconds
- **Bounce Detection**: 1-30 minutes

## 🎉 You're All Set!

Once everything is configured:
1. ✅ Contact form saves to database
2. ✅ Database trigger fires automatically
3. ✅ Edge Function sends emails via Mailgun
4. ✅ Webhooks track delivery status
5. ✅ Bounce alerts sent for failed deliveries
6. ✅ Complete email lifecycle tracking

Your robust email notification system is now live! 🚀

---

**Need Help?** Check the logs in Supabase Dashboard or Mailgun Dashboard for detailed error information.
