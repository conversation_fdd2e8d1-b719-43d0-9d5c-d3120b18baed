import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

interface ContactFormData {
  name: string
  email: string
  message: string
  source?: string
}

export async function POST(request: NextRequest) {
  try {
    console.log('📧 Contact form API called')

    // Validate environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('❌ Missing Supabase configuration')
      return NextResponse.json(
        {
          success: false,
          error: 'Server configuration error',
        },
        { status: 500 }
      )
    }

    // Parse request body
    const formData: ContactFormData = await request.json()
    console.log('📝 Form data received:', {
      name: formData.name,
      email: formData.email,
      source: formData.source,
    })

    // Validate required fields
    if (!formData.name || !formData.email || !formData.message) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
        },
        { status: 400 }
      )
    }

    // Create Supabase client with service role for database operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Save to contacts table
    const { data: submission, error: dbError } = await supabase
      .from('contacts')
      .insert([
        {
          name: formData.name,
          email: formData.email,
          message: formData.message,
          source: formData.source || 'contact_modal',
          created_at: new Date().toISOString(),
          email_status: 'pending',
          delivery_attempts: 0,
        },
      ])
      .select()
      .single()

    if (dbError) {
      console.error('❌ Database error:', dbError)
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to save submission',
        },
        { status: 500 }
      )
    }

    console.log('✅ Submission saved to database:', submission.id)

    // Call the Edge Function to send emails
    const edgeFunctionUrl = `${supabaseUrl}/functions/v1/send-contact-email`

    const emailResponse = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: submission.id,
        name: formData.name,
        email: formData.email,
        message: formData.message,
        source: formData.source || 'contact_modal',
        created_at: submission.created_at,
      }),
    })

    const emailResult = await emailResponse.json()

    if (!emailResponse.ok || !emailResult.success) {
      console.error('❌ Email sending failed:', emailResult)
      // Don't fail the whole request if email fails
      return NextResponse.json({
        success: true,
        message:
          'Form submitted successfully, but email notification may have failed',
        submissionId: submission.id,
        emailError: emailResult.error,
      })
    }

    console.log('✅ Emails sent successfully')

    return NextResponse.json({
      success: true,
      message: 'Message sent successfully!',
      submissionId: submission.id,
      emailResult: emailResult,
    })
  } catch (error) {
    console.error('❌ Contact API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Contact API endpoint',
    methods: ['POST'],
    timestamp: new Date().toISOString(),
  })
}
